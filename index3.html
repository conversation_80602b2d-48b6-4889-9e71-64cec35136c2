<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Clayboan Scroll Animation | Codegrid</title>
    <!-- <link rel="stylesheet" href="/styles.css" /> -->
    <style>
        @import url("https://fonts.googleapis.com/css2?family=Inter+Tight:ital,wght@0,100..900;1,100..900&display=swap");

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Inter Tight", sans-serif;
            background-color: #fcfcfc;
            color: #141414;
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
       
        h1 {
            text-transform: uppercase;
            text-align: center;
            font-size: 5rem;
            font-weight: 500;
            line-height: 1;
        }
        section {
            position: relative;
            width: 100vw;
            overflow: hidden;
        }
        .hero,
        .outro {
            height: 100svh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        .work-item {
            height: 150svh;
        }
        .work-item-img {
            position: relative;
            width: 100%;
            height: 100%;
            clip-path: polygon(25% 25%, 75% 40%, 100% 100%, 0% 100%);
            will-change: clip-path;
        }   
        .work-item-name {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            z-index: 1;
        }
        .work-item-name h1 {
            color: #fff;
        }
        @media (max-width: 1000px){
            h1,
            .work-item-name h1 {
                font-size: 2.5rem;
            }
        }
     
        </style>
</head>
<body>
    
    <section class="hero">
        <h1>Beyond the limits</h1>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/ptiCourtRoom.png" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/ptiCourtRoom.png" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/justice-Blind.jpg" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/ptiCourtRoom.png" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/justice-Blind.jpg" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="outro">
        <h1>Back to base</h1>
    </section>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>

    <script src="https://unpkg.com/@studio-freight/lenis@1.0.27/dist/lenis.min.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", () => {
            gsap.registerPlugin(ScrollTrigger);

            // Initialize Lenis with error handling
            let lenis;
            try {
                lenis = new Lenis({
                    duration: 1.2,
                    easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
                    smooth: true
                });

                lenis.on("scroll", ScrollTrigger.update);

                gsap.ticker.add((time) => {
                    lenis.raf(time * 1000);
                });
            } catch (error) {
                console.log("Lenis not available, using default scroll");
            }

            gsap.ticker.lagSmoothing(0);

            // Define unique clip-path configurations for each work item
            const clipPathConfigs = [
                {
                    // Item 0: Diamond to rectangle to angled (original)
                    initial: "polygon(25% 25%, 75% 40%, 100% 100%, 0% 100%)",
                    revealed: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
                    exit: "polygon(0% 0%, 100% 0%, 75% 60%, 25% 75%)",
                    textTiming: { start: "top 80%", end: "top 20%" },
                    revealTiming: { start: "top bottom", end: "top 30%" },
                    exitTiming: { start: "bottom 70%", end: "bottom top" }
                },
                {
                    // Item 1: Horizontal wipe from left
                    initial: "polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)",
                    revealed: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
                    exit: "polygon(80% 0%, 100% 0%, 100% 100%, 60% 100%)",
                    textTiming: { start: "top 85%", end: "top 15%" },
                    revealTiming: { start: "top bottom", end: "top 25%" },
                    exitTiming: { start: "bottom 75%", end: "bottom top" }
                },
                {
                    // Item 2: Diagonal wipe from bottom-left
                    initial: "polygon(0% 100%, 0% 100%, 0% 100%)",
                    revealed: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
                    exit: "polygon(100% 0%, 100% 0%, 100% 100%)",
                    textTiming: { start: "top 85%", end: "top 15%" },
                    revealTiming: { start: "top bottom", end: "top 30%" },
                    exitTiming: { start: "bottom 70%", end: "bottom top" }
                },
                {
                    // Item 3: Triangular reveal from center
                    initial: "polygon(50% 50%, 50% 50%, 50% 50%)",
                    revealed: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
                    exit: "polygon(0% 0%, 50% 0%, 25% 100%)",
                    textTiming: { start: "top 80%", end: "top 20%" },
                    revealTiming: { start: "top bottom", end: "top 25%" },
                    exitTiming: { start: "bottom 75%", end: "bottom top" }
                },
                {
                    // Item 4: Hexagonal reveal
                    initial: "polygon(50% 0%, 50% 0%, 50% 100%, 50% 100%)",
                    revealed: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
                    exit: "polygon(20% 0%, 80% 0%, 100% 50%, 80% 100%, 20% 100%, 0% 50%)",
                    textTiming: { start: "top 85%", end: "top 15%" },
                    revealTiming: { start: "top bottom", end: "top 30%" },
                    exitTiming: { start: "bottom 70%", end: "bottom top" }
                }
            ];

            gsap.utils.toArray(".work-item").forEach((item, index) => {
                const img = item.querySelector(".work-item-img");
                const nameH1 = item.querySelector(".work-item-name h1");

                // Get configuration for this specific item
                const config = clipPathConfigs[index] || clipPathConfigs[0]; // Fallback to first config

                console.log(`Setting up animations for item ${index} with config:`, config);

                // Free alternative to SplitText
                const text = nameH1.textContent;
                nameH1.innerHTML = text.split('').map(char =>
                    `<span style="display: inline-block; overflow: hidden;">${char === ' ' ? '&nbsp;' : char}</span>`
                ).join('');
                const chars = nameH1.querySelectorAll('span');

                // Set initial state for image with unique clip-path
                gsap.set(img, {
                    clipPath: config.initial
                });

                // Text animation with custom timing
                gsap.set(chars, { y: "125%" });

                ScrollTrigger.create({
                    trigger: item,
                    start: config.textTiming.start,
                    end: config.textTiming.end,
                    scrub: 1, // Consistent scrub value for smooth animation
                    animation: gsap.fromTo(chars, {
                        y: "125%"
                    }, {
                        y: "0%",
                        stagger: 0.03,
                        ease: "none"
                    })
                });

                // Image reveal animation with custom timing and clip-path
                ScrollTrigger.create({
                    trigger: item,
                    start: config.revealTiming.start,
                    end: config.revealTiming.end,
                    scrub: 1, // Consistent scrub for smooth reveal
                    animation: gsap.fromTo(img, {
                        clipPath: config.initial
                    }, {
                        clipPath: config.revealed,
                        ease: "none"
                    })
                });

                // Image exit animation with custom timing and clip-path
                ScrollTrigger.create({
                    trigger: item,
                    start: config.exitTiming.start,
                    end: config.exitTiming.end,
                    scrub: 1, // Consistent scrub to prevent snapping
                    animation: gsap.fromTo(img, {
                        clipPath: config.revealed
                    }, {
                        clipPath: config.exit,
                        ease: "none"
                    })
                });
            });
        });
    </script>
</body>
</html>