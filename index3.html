<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Clayboan Scroll Animation | Codegrid</title>
    <!-- <link rel="stylesheet" href="/styles.css" /> -->
    <style>
        @import url("https://fonts.googleapis.com/css2?family=Inter+Tight:ital,wght@0,100..900;1,100..900&display=swap");

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Inter Tight", sans-serif;
            background-color: #fcfcfc;
            color: #141414;
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
       
        h1 {
            text-transform: uppercase;
            text-align: center;
            font-size: 5rem;
            font-weight: 500;
            line-height: 1;
        }
        section {
            position: relative;
            width: 100vw;
            overflow: hidden;
        }
        .hero,
        .outro {
            height: 100svh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }
        .hero-video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: -1;
        }
        .hero-content {
            position: relative;
            z-index: 1;
            text-align: center;
        }
        .hero h1 {
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem 2rem;
            border-radius: 10px;
            backdrop-filter: blur(5px);
        }
        .work-item {
            height: 150svh;
        }
        .work-item-img {
            position: relative;
            width: 100%;
            height: 100%;
            clip-path: polygon(25% 25%, 75% 40%, 100% 100%, 0% 100%);
            will-change: clip-path;
        }   
        .work-item-name {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            z-index: 100;
            pointer-events: none;
        }
        .work-item-name h1 {
            pointer-events: auto;
        }
        .work-item-name h1 {
            color: #fff;
        }
        .work-item-video {
            position: relative;
            width: 100%;
            height: 100%;
            clip-path: polygon(25% 25%, 75% 40%, 100% 100%, 0% 100%);
            will-change: clip-path;
        }
        .work-item-video video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .video-play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.95);
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
            outline: none;
        }
        .video-play-button:hover {
            background: rgba(255, 255, 255, 1);
            transform: translate(-50%, -50%) scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        }
        .video-play-button::after {
            content: '';
            width: 0;
            height: 0;
            border-left: 25px solid #333;
            border-top: 15px solid transparent;
            border-bottom: 15px solid transparent;
            margin-left: 6px;
        }
        .video-play-button.hidden {
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }
        .video-play-button:active {
            transform: translate(-50%, -50%) scale(0.95);
        }
        @media (max-width: 1000px){
            h1,
            .work-item-name h1 {
                font-size: 2.5rem;
            }
        }
     
        </style>
</head>
<body>
    
    <section class="hero">
        <video class="hero-video" autoplay muted loop playsinline>
            <source src="img/IMG_1497_Philis_video.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        <div class="hero-content">
            <h1>Beyond the limits</h1>
        </div>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/ptiCourtRoom.png" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="work-item">
        <div class="work-item-video">
            <video preload="metadata" controls="false">
                <source src="img/justiceStatue2.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video>
            <button class="video-play-button" onclick="playVideo(this)"></button>
        </div>
        <div class="work-item-name">
            <h1>Justice in Motion</h1>
        </div>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/justice-Blind.jpg" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/ptiCourtRoom.png" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/justice-Blind.jpg" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="outro">
        <h1>Back to base</h1>
    </section>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>

    <script src="https://unpkg.com/@studio-freight/lenis@1.0.27/dist/lenis.min.js"></script>
    <script>
        // Function to play video when play button is clicked
        function playVideo(button) {
            console.log('Play button clicked!', button);
            const videoContainer = button.parentElement;
            const video = videoContainer.querySelector('video');

            console.log('Video container:', videoContainer);
            console.log('Video element:', video);

            if (video) {
                console.log('Attempting to play video...');
                video.play().then(() => {
                    console.log('Video started playing successfully');
                    button.classList.add('hidden');
                }).catch((error) => {
                    console.error('Error playing video:', error);
                });

                // Show play button again when video ends
                video.addEventListener('ended', () => {
                    console.log('Video ended');
                    button.classList.remove('hidden');
                });

                // Show play button when video is paused
                video.addEventListener('pause', () => {
                    console.log('Video paused');
                    if (video.currentTime < video.duration) {
                        button.classList.remove('hidden');
                    }
                });
            } else {
                console.error('Video element not found!');
            }
        }

        // Backup event listener approach
        document.addEventListener('DOMContentLoaded', () => {
            const playButtons = document.querySelectorAll('.video-play-button');
            playButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Event listener triggered');
                    playVideo(button);
                });
            });
        });

        document.addEventListener("DOMContentLoaded", () => {
            gsap.registerPlugin(ScrollTrigger);

            // Initialize Lenis with error handling
            let lenis;
            try {
                lenis = new Lenis({
                    duration: 1.2,
                    easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
                    smooth: true
                });

                lenis.on("scroll", ScrollTrigger.update);

                gsap.ticker.add((time) => {
                    lenis.raf(time * 1000);
                });
            } catch (error) {
                console.log("Lenis not available, using default scroll");
            }

            gsap.ticker.lagSmoothing(0);

            // Define clip-path configurations with symmetrical diamond reveal and exit animations
            const clipPathConfigs = [
                {
                    // Item 0: Symmetrical diamond animation - diamond to rectangle to diamond
                    initial: "polygon(50% 20%, 80% 50%, 50% 80%, 20% 50%)",
                    revealed: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
                    exit: "polygon(50% 20%, 80% 50%, 50% 80%, 20% 50%)",
                    textTiming: { start: "top 80%", end: "top 20%" },
                    revealTiming: { start: "top bottom", end: "top 25%" },
                    exitTiming: { start: "bottom 75%", end: "bottom top" }
                },
                {
                    // Item 1: Symmetrical diamond animation - diamond to rectangle to diamond
                    initial: "polygon(50% 20%, 80% 50%, 50% 80%, 20% 50%)",
                    revealed: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
                    exit: "polygon(50% 20%, 80% 50%, 50% 80%, 20% 50%)",
                    textTiming: { start: "top 85%", end: "top 15%" },
                    revealTiming: { start: "top bottom", end: "top 25%" },
                    exitTiming: { start: "bottom 75%", end: "bottom top" }
                },
                {
                    // Item 2: Symmetrical diamond animation - diamond to rectangle to diamond
                    initial: "polygon(50% 20%, 80% 50%, 50% 80%, 20% 50%)",
                    revealed: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
                    exit: "polygon(50% 20%, 80% 50%, 50% 80%, 20% 50%)",
                    textTiming: { start: "top 85%", end: "top 15%" },
                    revealTiming: { start: "top bottom", end: "top 25%" },
                    exitTiming: { start: "bottom 75%", end: "bottom top" }
                },
                {
                    // Item 3: Symmetrical diamond animation - diamond to rectangle to diamond
                    initial: "polygon(50% 20%, 80% 50%, 50% 80%, 20% 50%)",
                    revealed: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
                    exit: "polygon(50% 20%, 80% 50%, 50% 80%, 20% 50%)",
                    textTiming: { start: "top 80%", end: "top 20%" },
                    revealTiming: { start: "top bottom", end: "top 25%" },
                    exitTiming: { start: "bottom 75%", end: "bottom top" }
                },
                {
                    // Item 4: Symmetrical diamond animation - diamond to rectangle to diamond
                    initial: "polygon(50% 20%, 80% 50%, 50% 80%, 20% 50%)",
                    revealed: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
                    exit: "polygon(50% 20%, 80% 50%, 50% 80%, 20% 50%)",
                    textTiming: { start: "top 85%", end: "top 15%" },
                    revealTiming: { start: "top bottom", end: "top 25%" },
                    exitTiming: { start: "bottom 75%", end: "bottom top" }
                }
            ];

            gsap.utils.toArray(".work-item").forEach((item, index) => {
                const img = item.querySelector(".work-item-img");
                const video = item.querySelector(".work-item-video");
                const nameH1 = item.querySelector(".work-item-name h1");

                // Determine which element to animate (img or video container)
                const animatedElement = img || video;

                // Get configuration for this specific item
                const config = clipPathConfigs[index] || clipPathConfigs[0]; // Fallback to first config

                console.log(`Setting up animations for item ${index} with config:`, config);

                // Free alternative to SplitText
                const text = nameH1.textContent;
                nameH1.innerHTML = text.split('').map(char =>
                    `<span style="display: inline-block; overflow: hidden;">${char === ' ' ? '&nbsp;' : char}</span>`
                ).join('');
                const chars = nameH1.querySelectorAll('span');

                // Set initial state for image/video with unique clip-path
                if (animatedElement) {
                    gsap.set(animatedElement, {
                        clipPath: config.initial
                    });
                }

                // Text animation with custom timing
                gsap.set(chars, { y: "125%" });

                ScrollTrigger.create({
                    trigger: item,
                    start: config.textTiming.start,
                    end: config.textTiming.end,
                    scrub: 0.8, // Slightly faster for responsive text animation
                    animation: gsap.fromTo(chars, {
                        y: "125%"
                    }, {
                        y: "-160%",
                        stagger: 0.02, // Tighter stagger for smoother wave effect
                        ease: "none"
                    })
                });

                // Image/Video reveal animation with custom timing and clip-path
                if (animatedElement) {
                    ScrollTrigger.create({
                        trigger: item,
                        start: config.revealTiming.start,
                        end: config.revealTiming.end,
                        scrub: 1.2, // Slightly slower for dramatic polygon reveals
                        invalidateOnRefresh: true, // Prevents snapping on resize
                        animation: gsap.fromTo(animatedElement, {
                            clipPath: config.initial
                        }, {
                            clipPath: config.revealed,
                            ease: "none"
                        })
                    });

                    // Image/Video exit animation with custom timing and clip-path
                    ScrollTrigger.create({
                        trigger: item,
                        start: config.exitTiming.start,
                        end: config.exitTiming.end,
                        scrub: 1.2, // Matching reveal scrub for consistency
                        invalidateOnRefresh: true, // Prevents snapping on resize
                        animation: gsap.fromTo(animatedElement, {
                            clipPath: config.revealed
                        }, {
                            clipPath: config.exit,
                            ease: "none"
                        })
                    });
                }
            });
        });
    </script>
</body>
</html>