<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Clayboan Scroll Animation | Codegrid</title>
    <!-- <link rel="stylesheet" href="/styles.css" /> -->
    <style>
        @import url("https://fonts.googleapis.com/css2?family=Inter+Tight:ital,wght@0,100..900;1,100..900&display=swap");

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Inter Tight", sans-serif;
            background-color: #fcfcfc;
            color: #141414;
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
       
        h1 {
            text-transform: uppercase;
            text-align: center;
            font-size: 5rem;
            font-weight: 500;
            line-height: 1;
        }
        section {
            position: relative;
            width: 100vw;
            overflow: hidden;
        }
        .hero,
        .outro {
            height: 100svh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        .work-item {
            height: 150svh;
        }
        .work-item-img {
            position: relative;
            width: 100%;
            height: 100%;
            clip-path: polygon(25% 25%, 75% 40%, 100% 100%, 0% 100%);
            will-change: clip-path;
        }   
        .work-item-name {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            z-index: 1;
        }
        .work-item-name h1 {
            color: #fff;
        }
        @media (max-width: 1000px){
            h1,
            .work-item-name h1 {
                font-size: 2.5rem;
            }
        }
     
        </style>
</head>
<body>
    
    <section class="hero">
        <h1>Beyond the limits</h1>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/ptiCourtRoom.png" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/ptiCourtRoom.png" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/justice-Blind.jpg" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/ptiCourtRoom.png" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/ptiCourtRoom.png" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="outro">
        <h1>Back to base</h1>
    </section>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/lenis/1.2.1/lenis.min.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", () => {
            gsap.registerPlugin(ScrollTrigger);

            const lenis = new Lenis();
            
            lenis.on("scroll", ScrollTrigger.update);
            
            gsap.ticker.add((time) => {
                lenis.raf(time * 1000);
            });

            gsap.ticker.lagSmoothing(0);

            gsap.utils.toArray(".work-item").forEach((item) => {
                const img = item.querySelector(".work-item-img");
                const nameH1 = item.querySelector(".work-item-name h1");

                // Free alternative to SplitText
                const text = nameH1.textContent;
                nameH1.innerHTML = text.split('').map(char =>
                    `<span style="display: inline-block; transform: translateY(125%); overflow: hidden;">${char === ' ' ? '&nbsp;' : char}</span>`
                ).join('');
                const chars = nameH1.querySelectorAll('span');

                chars.forEach((char, index) => {
                    ScrollTrigger.create({
                        trigger: item,
                        start: `top+=${index * 25 - 250} top`,
                        end: `top+=${index * 25 - 100} top`,
                        scrub: 1,
                        animation: gsap.fromTo(
                        char,
                        {
                            y: "125%",
                        },
                        {
                            y: "0%",
                            ease: "none",
                        }
                        ),
                    });
                });
                ScrollTrigger.create({
                    trigger: item,
                    start: "top bottom",
                    end: "top top",
                    scrub: 0.5,
                    animation: gsap.fromTo(
                    img,
                    {
                        clipPath: "polygon(25% 25%, 75% 40%, 100% 100%, 0% 100%)",
                    },
                    {
                        clipPath: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
                        ease: "none",
                    }
                    ),
                });
                ScrollTrigger.create({ 
                    trigger: item,
                    start: "bottom bottom",
                    end: "bottom top",
                    scrub: 0.5,
                    animation: gsap.fromTo(
                    img,
                    {
            clipPath: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
                    },
                    {
                        clipPath: "polygon(0% 0%, 100% 0%, 75% 60%, 25% 75%)",
                        ease: "none",
                    }
                    ),
                }); 


            });
        });
    </script>
</body>
</html>