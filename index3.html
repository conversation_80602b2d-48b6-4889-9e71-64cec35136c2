<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Clayboan Scroll Animation | Codegrid</title>
    <!-- <link rel="stylesheet" href="/styles.css" /> -->
    <style>
        @import url("https://fonts.googleapis.com/css2?family=Inter+Tight:ital,wght@0,100..900;1,100..900&display=swap");

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Inter Tight", sans-serif;
            background-color: #fcfcfc;
            color: #141414;
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
       
        h1 {
            text-transform: uppercase;
            text-align: center;
            font-size: 5rem;
            font-weight: 500;
            line-height: 1;
        }
        section {
            position: relative;
            width: 100vw;
            overflow: hidden;
        }
        .hero,
        .outro {
            height: 100svh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        .work-item {
            height: 150svh;
        }
        .work-item-img {
            position: relative;
            width: 100%;
            height: 100%;
            clip-path: polygon(25% 25%, 75% 40%, 100% 100%, 0% 100%);
            will-change: clip-path;
        }   
        .work-item-name {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            z-index: 1;
        }
        .work-item-name h1 {
            color: #fff;
        }
        @media (max-width: 1000px){
            h1,
            .work-item-name h1 {
                font-size: 2.5rem;
            }
        }
     
        </style>
</head>
<body>
    
    <section class="hero">
        <h1>Beyond the limits</h1>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/ptiCourtRoom.png" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/ptiCourtRoom.png" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/justice-Blind.jpg" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/ptiCourtRoom.png" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="work-item">
        <div class="work-item-img"><img src="img/justice-Blind.jpg" alt="" /></div>
        <div class="work-item-name">
            <h1>Carbon Edge</h1>
        </div>
    </section>

    <section class="outro">
        <h1>Back to base</h1>
    </section>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>

    <script src="https://unpkg.com/@studio-freight/lenis@1.0.27/dist/lenis.min.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", () => {
            gsap.registerPlugin(ScrollTrigger);

            // Initialize Lenis with error handling
            let lenis;
            try {
                lenis = new Lenis({
                    duration: 1.2,
                    easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
                    smooth: true
                });

                lenis.on("scroll", ScrollTrigger.update);

                gsap.ticker.add((time) => {
                    lenis.raf(time * 1000);
                });
            } catch (error) {
                console.log("Lenis not available, using default scroll");
            }

            gsap.ticker.lagSmoothing(0);

            gsap.utils.toArray(".work-item").forEach((item, index) => {
                const img = item.querySelector(".work-item-img");
                const nameH1 = item.querySelector(".work-item-name h1");

                console.log(`Setting up animations for item ${index}`);

                // Free alternative to SplitText
                const text = nameH1.textContent;
                nameH1.innerHTML = text.split('').map(char =>
                    `<span style="display: inline-block; transform: translateY(125%); overflow: hidden;">${char === ' ' ? '&nbsp;' : char}</span>`
                ).join('');
                const chars = nameH1.querySelectorAll('span');

                // Set initial state for image - ensure it starts clipped
                gsap.set(img, {
                    clipPath: "polygon(25% 25%, 75% 40%, 100% 100%, 0% 100%)"
                });

                // Text animation - characters appear as section enters
                gsap.set(chars, { y: "125%" });

                ScrollTrigger.create({
                    trigger: item,
                    start: "top 80%",
                    end: "top 20%",
                    scrub: 1,
                    onUpdate: (self) => {
                        chars.forEach((char, i) => {
                            const progress = Math.max(0, self.progress - (i * 0.1));
                            gsap.set(char, { y: `${125 - (progress * 125)}%` });
                        });
                    }
                });

                // Image reveal animation - as section enters viewport
                ScrollTrigger.create({
                    trigger: item,
                    start: "top bottom",
                    end: "top 20%",
                    scrub: true,
                    onUpdate: (self) => {
                        const progress = self.progress;
                        // Interpolate between clipped and full reveal
                        const x1 = 25 - (progress * 25); // 25% to 0%
                        const y1 = 25 - (progress * 25); // 25% to 0%
                        const x2 = 75 + (progress * 25); // 75% to 100%
                        const y2 = 40 - (progress * 40); // 40% to 0%

                        gsap.set(img, {
                            clipPath: `polygon(${x1}% ${y1}%, ${x2}% ${y2}%, 100% 100%, 0% 100%)`
                        });
                    }
                });

                // Image exit animation - as section leaves viewport
                ScrollTrigger.create({
                    trigger: item,
                    start: "bottom 70%",
                    end: "bottom top",
                    scrub: true,
                    onUpdate: (self) => {
                        const progress = self.progress;
                        // Interpolate from full reveal to exit clip
                        const x2 = 100 - (progress * 25); // 100% to 75%
                        const y2 = 0 + (progress * 60);   // 0% to 60%
                        const x3 = 0 + (progress * 25);   // 0% to 25%
                        const y3 = 100 - (progress * 25); // 100% to 75%

                        gsap.set(img, {
                            clipPath: `polygon(0% 0%, ${x2}% ${y2}%, ${x3}% ${y3}%, 0% 100%)`
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>