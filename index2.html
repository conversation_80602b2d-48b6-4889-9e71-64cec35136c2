<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Scroll Reveal Clip Path</title>
<style>
  body {
    margin: 0;
    background: white;
    font-family: Arial, sans-serif;
    color: #333;
  }

  section {
    position: relative;
    height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  /* Intro Section */
    .intro {
      background: #111;
      font-size: 2rem;
    }

  .image-container {
    position: absolute;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    clip-path: circle(0% at 50% 50%);
  }

  /* Different shapes for variety */
  .shape-1 { clip-path: circle(0% at 50% 50%); }
  .shape-2 { clip-path: polygon(50% 50%, 50% 50%, 50% 50%); }
  .shape-3 { clip-path: ellipse(0% 0% at 50% 50%); }
  .shape-4 { clip-path: polygon(50% 0%, 100% 25%, 75% 100%, 25% 100%, 0% 25%); }
  .shape-5 { clip-path: polygon(25% 25%, 75 40%, 100% 100%, 0% 100%); }
  /*.shape-5 { clip-path: polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%); }*/ /* Hexagon */

  .content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    font-size: 3rem;
    font-weight: bold;
    text-shadow: 0 2px 10px rgba(0,0,0,0.5);
  }

  /* header, footer {
    padding: 50px;
    background: #111;
    color: white;
    text-align: center;
    font-size: 2rem;
  } */
    /* Outro Section */
    .outro {
      background: #ffffff;
      font-size: 2rem;
    }
</style>
</head>
<body>


  <!-- Intro -->
  <section class="intro">
    <h1>Welcome to the Scroll Mask Animation</h1>
  </section>

<section>
  <div class="image-container shape-1" style="background-image: url('img/ptiCourtRoom.png');"></div>
  <div class="content">Discover Nature</div>
</section>

<section>
  <div class="image-container shape-2" style="background-image: url('img/justice-Blidn.jpg');"></div>
  <div class="content">Urban Adventure</div>
</section>

<section>
  <div class="image-container shape-3" style="background-image: url('img/ptiCourtRoom.png');"></div>
  <div class="content">Ocean Views</div>
</section>

<section>
  <div class="image-container shape-4" style="background-image: url('img/justice-Blidn.jpg');"></div>
  <div class="content">Mountain Escape</div>
</section>

<section>
  <div class="image-container shape-5" style="background-image: url('img/justice-Blidn.jpg');"></div>
  <div class="content">Desert Wonders</div>
</section>

 <!-- Outro -->
  <section class="outro">
    <h1>Thanks for Scrolling!</h1>
  </section>
  

<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
<script>
  gsap.registerPlugin(ScrollTrigger);

  // Shape-specific animations
  const shapeAnimations = {
    "shape-1": { from: "circle(0% at 50% 50%)", to: "circle(150% at 50% 50%)" },
    "shape-2": { from: "polygon(50% 50%, 50% 50%, 50% 50%)", to: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)" },
    "shape-3": { from: "ellipse(0% 0% at 50% 50%)", to: "ellipse(100% 100% at 50% 50%)" },
    "shape-4": { from: "polygon(50% 0%, 50% 0%, 50% 100%, 50% 100%, 50% 0%)", to: "polygon(50% 0%, 100% 25%, 75% 100%, 25% 100%, 0% 25%)" },
    "shape-5": { from: "polygon(50% 0%, 50% 0%, 50% 100%, 50% 100%, 50% 0%)", to: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)" }
  };

  document.querySelectorAll(".image-container").forEach(container => {
    const shapeClass = Array.from(container.classList).find(c => c.startsWith("shape-"));
    if (shapeAnimations[shapeClass]) {
      gsap.fromTo(container,
        { clipPath: shapeAnimations[shapeClass].from },
        {
          clipPath: shapeAnimations[shapeClass].to,
          ease: "power1.out",
          scrollTrigger: {
            trigger: container.parentElement,
            start: "top center",
            end: "bottom center",
            scrub: true
          }
        }
      );
    }
  });
</script>

</body>
</html>
