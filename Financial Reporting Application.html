<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Reporting Application</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        /* Style for the element being dragged */
        .dragging {
            opacity: 0.5;
            background: #cde6ff;
            border: 1px dashed #007bff;
        }
        /* Custom scrollbar for a cleaner look */
        .custom-scrollbar::-webkit-scrollbar {
            width: 8px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        /* Styles for table column resizing */
        .resizable-table {
            table-layout: fixed;
        }
        .resizable-table th {
            position: relative;
        }
        .resizer {
            position: absolute;
            top: 0;
            right: 0;
            width: 5px;
            cursor: col-resize;
            user-select: none;
            height: 100%;
            background-color: transparent;
            transition: background-color 0.2s;
        }
        th:hover .resizer {
            background-color: #a0aec0;
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800">

<div class="flex h-screen">
    <!-- Main Content Area (Placeholder) -->
    <main class="flex-1 p-6 overflow-y-auto custom-scrollbar">
        <div class="bg-white p-6 rounded-xl shadow-lg h-full">
            <h1 class="text-2xl font-bold text-gray-700 mb-4">Reporting Dashboard</h1>
            <p class="text-gray-600 mb-6">This area represents the main content of your application, such as data tables and charts.</p>
            <div class="w-full h-96 bg-gray-200 rounded-lg flex items-center justify-center">
                <span class="text-gray-500">Main Application Content Placeholder</span>
            </div>
        </div>
    </main>

    <!-- Right Side Rail for Configuration -->
    <aside class="w-full max-w-sm bg-white p-6 shadow-2xl overflow-y-auto custom-scrollbar">
        <div class="border border-gray-300 rounded-lg p-4">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Report PDF Configuration</h2>

            <div>
                <label class="flex items-center space-x-2 cursor-pointer">
                    <input type="radio" name="reportType" value="standard" class="form-radio text-blue-600">
                    <span class="text-sm font-medium">Standard Report</span>
                </label>
                <label class="flex items-center space-x-2 mt-2 cursor-pointer">
                    <input type="radio" name="reportType" value="custom" checked class="form-radio text-blue-600">
                    <span class="text-sm font-medium">Customize Report</span>
                </label>
            </div>

            <div id="customSection" class="mt-4">
                <p class="text-xs text-gray-600 mb-2">
                    Select the columns you want to include in your PDF report using the checkboxes below.
                </p>
                <p class="text-xs text-gray-600 mb-4">
                     Each column has a pixel width, and the printable space on the PDF is limited (to 840 pixels). Deselect columns to ensure everything fits.
                </p>

                <div id="itemsContainer" class="space-y-2">
                    <!-- Draggable items will be dynamically inserted here -->
                </div>

                <div id="validation-message" class="hidden mt-4 text-xs text-red-700 bg-red-100 border border-red-300 rounded-lg p-3"></div>

                <div class="mt-6">
                    <button id="show-preview-btn" class="w-full bg-gray-200 text-gray-800 font-bold py-2 px-4 rounded-lg hover:bg-gray-300 transition-all text-sm">
                        Show PDF PREVIEW
                    </button>
                </div>
            </div>
        </div>
    </aside>
</div>

<!-- Modal for Report Preview -->
<div id="report-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-75 overflow-y-auto h-full w-full flex items-center justify-center p-4 z-50">
    <div class="relative mx-auto p-5 border w-full max-w-7xl shadow-lg rounded-xl bg-white">
        <div class="absolute top-0 right-0 pt-4 pr-4">
            <button id="close-modal-btn" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
            </button>
        </div>
        <div id="modal-content-container" class="mt-3">
             <!-- Report table will be injected here -->
        </div>
    </div>
</div>


<script>
    // --- DATA STORE ---
    const reportData = [
        { "account-number": "x1700", "account-name": "JANET W BEAN FAMILY TRUST", "estimated-annual-income": 92918, "unrealized-gl": 3597550, "ytd-realized-gl": 92918, "total-account-mv": 3597550 },
        { "account-number": "x1800", "account-name": "RICHARD C BEAN FAMILY TRUST", "estimated-annual-income": 79506, "unrealized-gl": 3185251, "ytd-realized-gl": 79506, "total-account-mv": 3185251 },
        { "account-number": "x1400", "account-name": "HENRY D BEAN & SONS PENSION PLAN", "estimated-annual-income": 46850, "unrealized-gl": 2202438, "ytd-realized-gl": 46850, "total-account-mv": 2202438 },
        { "account-number": "x8200", "account-name": "HENRY D BEAN SR FAMILY TRUST U/A 7/21/94 FBO HENRY D BEAN JR", "estimated-annual-income": 43498, "unrealized-gl": 2129180, "ytd-realized-gl": 43498, "total-account-mv": 2129180 },
        { "account-number": "x8300", "account-name": "HENRY D BEAN SR FAMILY TRUST U/A 7/21/94 FBO RICHARD C BEAN", "estimated-annual-income": 37159, "unrealized-gl": 2012294, "ytd-realized-gl": 37159, "total-account-mv": 2012294 },
    ];

    reportData.forEach(item => {
        item['investment-objective'] = 'Growth and Income';
        item['asset-allocation-mv'] = (item['total-account-mv'] * 0.95).toFixed(2);
        item['asset-allocation-perc'] = '95.0%';
    });

    const totalsData = {
        "estimated-annual-income": 300000, "unrealized-gl": ********, "ytd-realized-gl": 300000, "total-account-mv": ********, "asset-allocation-mv": (******** * 0.95).toFixed(2)
    };
    
    // --- COLUMN CONFIGURATION ---
    let columnConfig = [
        { id: 'account-name', text: 'Account Name', width: 150, isNumeric: false, checked: true },
        { id: 'account-number', text: 'Account Number', width: 120, isNumeric: false, checked: true },
        { id: 'investment-objective', text: 'Investment Objective', width: 180, isNumeric: false, checked: false },
        { id: 'asset-allocation-mv', text: 'Asset Allocation Market Value', width: 200, isNumeric: true, checked: false },
        { id: 'total-account-mv', text: 'Total Account Market Value', width: 180, isNumeric: true, checked: true, locked: true },
        { id: 'estimated-annual-income', text: 'Estimated Annual Income', width: 200, isNumeric: true, checked: false },
        { id: 'unrealized-gl', text: 'Unrealized G/L', width: 150, isNumeric: true, checked: false },
        { id: 'ytd-realized-gl', text: 'YTD Realized G/L', width: 150, isNumeric: true, checked: false },
    ];
    
    // --- DOM ELEMENT REFERENCES ---
    const reportRadios = document.getElementsByName('reportType');
    const customSection = document.getElementById('customSection');
    const itemsContainer = document.getElementById('itemsContainer');
    const validationMessage = document.getElementById('validation-message');
    
    // --- INITIALIZATION ---
    function initializeColumnList() {
        itemsContainer.innerHTML = '';
        columnConfig.forEach(col => {
            const isLocked = col.locked;
            const itemHTML = `<div class="draggable flex items-center justify-between p-2 bg-gray-50 border border-gray-200 rounded-md" draggable="true" data-column-id="${col.id}" data-width="${col.width}"><div class="flex items-center"><input type="checkbox" class="form-checkbox h-4 w-4 text-blue-600 column-checkbox" ${col.checked ? 'checked' : ''} ${isLocked ? 'disabled' : ''}><label class="ml-2 text-sm text-gray-700">${col.text}</label></div><span class="drag-icon cursor-move text-gray-500">☰</span></div>`;
            itemsContainer.innerHTML += itemHTML;
        });
        addEventListenersToColumns();
        updateValidation();
    }

    function addEventListenersToColumns() {
        document.querySelectorAll('.column-checkbox').forEach(cb => {
            cb.addEventListener('change', updateValidation);
        });
        document.querySelectorAll('.draggable').forEach(draggable => {
            draggable.addEventListener('dragstart', () => draggable.classList.add('dragging'));
            draggable.addEventListener('dragend', () => draggable.classList.remove('dragging'));
        });
    }
    
    // --- EVENT LISTENERS ---
    reportRadios.forEach(radio => radio.addEventListener('change', () => {
        customSection.style.display = radio.value === 'custom' ? 'block' : 'none';
    }));

    itemsContainer.addEventListener('dragover', e => {
        e.preventDefault();
        const afterElement = getDragAfterElement(itemsContainer, e.clientY);
        const dragging = document.querySelector('.dragging');
        if (afterElement == null) {
            itemsContainer.appendChild(dragging);
        } else {
            itemsContainer.insertBefore(dragging, afterElement);
        }
    });

    function getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('.draggable:not(.dragging)')];
        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;
            if (offset < 0 && offset > closest.offset) {
                return { offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }

    // --- VALIDATION LOGIC ---
    function updateValidation() {
        let totalWidth = 0;
        let selectedCount = 0;
        
        // Re-read widths from the config object as it may have been updated by the modal
        const currentColumnOrder = [...itemsContainer.querySelectorAll('.draggable')].map(el => el.dataset.columnId);
        
        currentColumnOrder.forEach(id => {
            const el = itemsContainer.querySelector(`.draggable[data-column-id="${id}"]`);
            const checkbox = el.querySelector('.column-checkbox');
            if(checkbox.checked) {
                selectedCount++;
                const config = columnConfig.find(c => c.id === id);
                if (config) {
                    totalWidth += config.width;
                }
            }
        });
        
        const MAX_COLUMNS = 8;
        const MAX_PDF_WIDTH = 840;
        let msg = '';
        
        if (totalWidth > MAX_PDF_WIDTH) {
             msg = `Total width ${Math.round(totalWidth)}px exceeds the maximum printable width of ${MAX_PDF_WIDTH}px.`;
        } else if (selectedCount > MAX_COLUMNS) {
            msg = `You have selected ${selectedCount} columns. The maximum is ${MAX_COLUMNS}.`;
        }

        validationMessage.classList.remove('hidden', 'text-red-700', 'bg-red-100', 'border-red-300', 'text-green-700', 'bg-green-100', 'border-green-300');
        if (msg) {
            validationMessage.textContent = msg;
            validationMessage.classList.add('text-red-700', 'bg-red-100', 'border-red-300');
        } else {
            validationMessage.textContent = `${selectedCount} columns selected. Total width: ${Math.round(totalWidth)}px.`;
            validationMessage.classList.add('text-green-700', 'bg-green-100', 'border-green-300');
        }
    }

    // --- REPORT GENERATION & MODAL LOGIC ---
    const showPreviewBtn = document.getElementById('show-preview-btn');
    const reportModal = document.getElementById('report-modal');
    const modalContentContainer = document.getElementById('modal-content-container');
    const closeModalBtn = document.getElementById('close-modal-btn');

    showPreviewBtn.addEventListener('click', () => {
        const selectedColumns = [];
        const orderedColumnIds = [...itemsContainer.querySelectorAll('.draggable')].map(el => el.dataset.columnId);
        
        orderedColumnIds.forEach(id => {
            const el = itemsContainer.querySelector(`.draggable[data-column-id="${id}"]`);
            if (el.querySelector('.column-checkbox').checked) {
                // Get a fresh copy of config to avoid mutation issues
                const config = JSON.parse(JSON.stringify(columnConfig.find(c => c.id === id)));
                if (config) {
                    selectedColumns.push(config);
                }
            }
        });

        const currencyFormatter = new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' });
        
        let headerHtml = '';
        selectedColumns.forEach(col => {
            headerHtml += `<th scope="col" data-column-id="${col.id}" style="width: ${col.width}px;" class="px-6 py-3 whitespace-normal ${col.isNumeric ? 'text-right' : 'text-left'}">${col.text}<div class="resizer"></div></th>`;
        });

        let bodyHtml = '';
        reportData.forEach(rowData => {
            bodyHtml += `<tr class="bg-white border-b hover:bg-gray-50">`;
            selectedColumns.forEach(col => {
                const value = rowData[col.id] || 'N/A';
                const formattedValue = col.isNumeric ? currencyFormatter.format(value) : value;
                bodyHtml += `<td class="px-6 py-4 ${col.isNumeric ? 'text-right' : ''}">${formattedValue}</td>`;
            });
            bodyHtml += `</tr>`;
        });
        
        let footerHtml = `<tr>`;
        selectedColumns.forEach(col => {
            const totalValue = totalsData[col.id];
            let formattedTotal = '';
            if (totalValue !== undefined) {
                 formattedTotal = col.isNumeric ? currencyFormatter.format(totalValue) : totalValue;
            } else if (col.id === 'account-name') {
                 formattedTotal = 'Total Bean Family Relationship';
            }
            footerHtml += `<td class="px-6 py-3 ${col.isNumeric ? 'text-right' : ''}">${formattedTotal}</td>`;
        });
        footerHtml += `</tr>`;

        const fullTableHtml = `
            <div class="text-center mb-4">
                <h2 class="text-xl font-bold">Fiduciary Trust International</h2>
                <h3 class="text-lg font-semibold">Asset Allocation Summary</h3>
            </div>
            <div class="mb-4 p-2 bg-gray-100 rounded-md text-sm font-medium text-center">
                Printable Width Status: 
                <span id="total-width-indicator" class="font-bold"></span> / 840px
            </div>
            <div class="overflow-x-auto">
                <table id="preview-table" class="text-sm resizable-table">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50"><tr>${headerHtml}</tr></thead>
                    <tbody>${bodyHtml}</tbody>
                    <tfoot class="font-semibold text-gray-900 bg-gray-50">${footerHtml}</tfoot>
                </table>
            </div>`;

        modalContentContainer.innerHTML = fullTableHtml;
        reportModal.classList.remove('hidden');
        makeTableResizable();
    });

    // --- TABLE RESIZING LOGIC ---
    function makeTableResizable() {
        const table = document.getElementById('preview-table');
        const headers = table.querySelectorAll('th');
        let thBeingResized;
        let startOffset;

        headers.forEach(th => {
            const resizer = th.querySelector('.resizer');
            resizer.addEventListener('mousedown', (e) => {
                thBeingResized = th;
                startOffset = th.offsetWidth - e.pageX;
                document.body.style.cursor = 'col-resize';
                document.addEventListener('mousemove', onMouseMove);
                document.addEventListener('mouseup', onMouseUp, { once: true });
            });
        });

        const onMouseMove = (e) => {
            if (!thBeingResized) return;
            const newWidth = startOffset + e.pageX;
            if (newWidth > 50) { // Set a minimum width
                 thBeingResized.style.width = `${newWidth}px`;
            }
            updateTotalWidthIndicator();
        };

        const onMouseUp = () => {
            if(thBeingResized) {
                const columnId = thBeingResized.dataset.columnId;
                const config = columnConfig.find(c => c.id === columnId);
                if (config) {
                    config.width = parseInt(thBeingResized.style.width);
                }
            }
            document.body.style.cursor = '';
            thBeingResized = undefined;
            document.removeEventListener('mousemove', onMouseMove);
        };
        updateTotalWidthIndicator();
    }

    function updateTotalWidthIndicator() {
        const table = document.getElementById('preview-table');
        if(!table) return;

        const headers = table.querySelectorAll('th');
        let totalWidth = 0;
        headers.forEach(th => {
            totalWidth += th.offsetWidth;
        });

        const indicator = document.getElementById('total-width-indicator');
        indicator.textContent = `${Math.round(totalWidth)}px`;
        
        indicator.classList.remove('text-red-600', 'text-green-600');
        if (totalWidth > 840) {
            indicator.classList.add('text-red-600');
        } else {
            indicator.classList.add('text-green-600');
        }
    }

    closeModalBtn.addEventListener('click', () => {
        reportModal.classList.add('hidden');
        updateValidation(); // Update side rail validation after closing modal
    });
    reportModal.addEventListener('click', e => {
        if (e.target.id === 'report-modal') {
             reportModal.classList.add('hidden');
             updateValidation(); // Update side rail validation after closing modal
        }
    });

    // --- INITIALIZE THE APP ---
    document.addEventListener('DOMContentLoaded', initializeColumnList);

</script>

</body>
</html>

