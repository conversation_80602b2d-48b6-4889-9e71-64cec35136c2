<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Scroll Mask Animation</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: Arial, sans-serif;
      background: #000;
      color: white;
      overflow-x: hidden;
    }

    section {
      position: relative;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 2rem;
    }

    /* Intro Section */
    .intro {
      background: #111;
      font-size: 2rem;
    }

    /* Image Sections */
    .image-section {
      position: relative;
      overflow: hidden;
    }

    .image-section img {
      position: absolute;
      width: 100%;
      height: 100%;
      object-fit: cover;
      top: 0;
      left: 0;
      clip-path: circle(0% at 50% 50%);
      transition: clip-path 0.3s ease;
    }

    .image-section .overlay {
      position: relative;
      z-index: 2;
      font-size: 2rem;
      background: rgba(0,0,0,0.4);
      padding: 1rem 2rem;
      border-radius: 8px;
    }

    /* Outro Section */
    .outro {
      background: #111;
      font-size: 2rem;
    }
  </style>
</head>
<body>

  <!-- Intro -->
  <section class="intro">
    <h1>Welcome to the Scroll Mask Animation</h1>
  </section>

  <!-- Image Sections -->
  <section class="image-section">
    <img src="img/ptiCourtRoom.png" alt="Image 1" />
    <div class="overlay">PRE TRIAL Intervention</div>
  </section>

  <section class="image-section">
    <img src="img/justice-Blidn.jpg" alt="Image 2" />
    <div class="overlay">Another Section</div>
  </section>

  <section class="image-section">
    <img src="https://via.placeholder.com/1600x900/0000ff/ffffff" alt="Image 3" />
    <div class="overlay">Final Image Section</div>
  </section>

  <!-- Outro -->
  <section class="outro">
    <h1>Thanks for Scrolling!</h1>
  </section>

  <!-- GSAP & ScrollTrigger -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>

  <script>
    gsap.registerPlugin(ScrollTrigger);

    document.querySelectorAll(".image-section img").forEach((img) => {
      gsap.fromTo(img,
        { clipPath: "circle(0% at 50% 50%)" },
        {
          clipPath: "circle(75% at 50% 50%)",
          ease: "power1.out",
          scrollTrigger: {
            trigger: img.parentElement,
            start: "top center",
            end: "bottom center",
            scrub: true
          }
        }
      );
    });
  </script>
</body>
</html>
